import getpass
import socket
import traceback
import uuid
import asyncio

import aiohttp
from typing import List, Dict
import pandas as pd
import time
import yaml

from config.run_config import API_ACCESS_TOKEN, DOMAIN, RUN_CONFIG_DICT
from core.schema.chat_base import MessageType, Item
from core.schema.chat_request import ChatRequest, ChatRound, Message, Role, Area, Language, ResponseMode
from core.schema.chat_response import EventType
from util.common_util import decode_sse, is_empty, may_have_bad_markdown, not_empty
from util.df_util import put_cols_ahead, remove_empty_columns
from util.llm_util import translate_async
from util.score_ai_answer import score_ai_ans_parse_async

# ToDo(hm): 和 base_unit 合并下
async def get_response(session: aiohttp.ClientSession, query: str, item_name: str, env: str, timeout=60, version=None):
    url = f"http://{RUN_CONFIG_DICT[env][DOMAIN]}/api/v1/chat"
    headers = {
        "Connection": "keep-alive",
        "Content-Type": "application/json",
        "Authorization": RUN_CONFIG_DICT[env][API_ACCESS_TOKEN],
        "X-Request-ID": "streamlit",
    }
    request_id = f"{getpass.getuser()}@{socket.gethostname()}-{uuid.uuid4()}"

    # Create a 430 ChatRequest object
    if version is None:
        chat_request = ChatRequest(
            area=Area.INDONESIA,
            site=0,
            category_name="0",
            category_id="0",
            user_id="0",
            item_id="0",
            org_id="0",
            conversation_id="0",
            language=Language.INDONESIAN,
            item_name=item_name,
            chat_history=[
                ChatRound(
                    role=Role.USER,
                    messages=[
                        Message(
                            content=query,
                            type=MessageType.TEXT
                        )
                    ]
                )
            ],
            response_mode=ResponseMode.STREAMING,
            request_id=request_id,
            debug=True,
        )
    else:
        # Create a 530 ChatRequest object: version=1, put item name into selected_item
        chat_request = ChatRequest(
            area=Area.INDONESIA,
            site=0,
            category_name="0",
            category_id="0",
            user_id="0",
            item_id="0",
            org_id="0",
            conversation_id="0",
            language=Language.INDONESIAN,
            chat_history=[
                ChatRound(
                    role=Role.USER,
                    messages=[
                        Message(
                            content=query,
                            type=MessageType.TEXT,
                            selected_item=Item(item_id="123", item_name=item_name, category_id="smartphone",
                                               category_name="Smartphone")
                        )
                    ]
                )
            ],
            response_mode=ResponseMode.STREAMING,
            request_id=request_id,
            debug=True,
            version=1
        )

    # Convert the ChatRequest to a dictionary for the API request
    data = chat_request.to_dict()
    full_response = {
        "request_id": request_id,
        "data": {
            "text": "",
            "model_version": "",
        },
        'build_prompt': "",
        'time_cost': "",
        "first_token_time": "",
        "infer_time": "",
        "answer_type": "",
        "total_tokens": "",
    }
    try:
        start_time = time.time()
        # Add timeout to prevent hanging requests
        timeout_obj = aiohttp.ClientTimeout(total=timeout)
        async with session.post(url, headers=headers, json=data, timeout=timeout_obj) as response:
            try:
                async for line in response.content:
                    chunk = line.decode("utf-8").strip()
                    if is_empty(chunk):
                        continue

                    response_dict = decode_sse(chunk)
                    if is_empty(response_dict) or "event" not in response_dict:
                        continue

                    cur_event_type = response_dict["event"]
                    if cur_event_type == EventType.TEXT_CHUNK_EVENT.value:
                        full_response["data"]["text"] += response_dict["data"]["text"]

                    # ToDo(hm): 这里过时了，修改下：
                    if cur_event_type == EventType.FINISH_EVENT.value:
                        # 因为历史原因，这里的 request_id 其实是算法侧发过来的 response_id，应该用 request 中的 id
                        # full_response["request_id"] = response_dict["request_id"]
                        full_response["data"]["model_version"] = response_dict["data"]["model_version"]
                        full_response["time_cost"] = time.time() - start_time
                        full_response["first_token_time"] = (response_dict["data"]["answer_start_time"] -
                                                             response_dict["data"]["request_receive_time"]) / 1000
                        full_response["infer_time"] = (response_dict["data"]["answer_finish_time"] -
                                                       response_dict["data"][
                                                           "answer_start_time"]) / 1000
                        full_response["answer_type"] = answer_type_dict[response_dict["data"]['answer_type']]
                        full_response["total_tokens"] = response_dict["data"]["total_tokens"]
                        if full_response['answer_type'] == 'PASS':
                            full_response["build_prompt"] = response_dict["data"]["prompt"]

                        print("请求完成", full_response['request_id'], "耗时:", full_response["time_cost"])
                        return full_response
            except (aiohttp.ClientPayloadError, aiohttp.ClientConnectionError, asyncio.TimeoutError) as e:
                # Handle specific connection errors
                print(f"连接错误: {str(e)}")
                full_response["build_prompt"] = f"连接错误: {str(e)}"
                full_response["time_cost"] = time.time() - start_time
                return full_response
            except RuntimeError as e:
                # Handle client disconnection errors
                if "客户端已断开连接" in str(e):
                    print(f"客户端断开连接: {str(e)}")
                    full_response["build_prompt"] = f"客户端断开连接: {str(e)}"
                    full_response["time_cost"] = time.time() - start_time
                    return full_response
                raise  # Re-raise other RuntimeErrors

        # If we get here, we didn't receive a FINISH_EVENT
        full_response["time_cost"] = time.time() - start_time
        full_response["build_prompt"] = "未收到完整响应"
        return full_response
    except asyncio.TimeoutError:
        # Handle timeout
        full_response["build_prompt"] = f"请求超时 (>{timeout}秒)"
        full_response["time_cost"] = time.time() - start_time
        return full_response
    except Exception as e:
        # Handle all other exceptions
        error_msg = f"请求异常 {str(e)}：{traceback.format_exc()}"
        print(error_msg)
        full_response["build_prompt"] = error_msg
        full_response["time_cost"] = time.time() - start_time
        return full_response


async def process_batch(session: aiohttp.ClientSession, queries, env, val_score_flag: bool,
                        batch_num: int, max_retries=2, timeout=60, version=None, extra_col_names=None,
                        columns_info=None) -> List[Dict[str, any]]:
    results = []
    tasks = []

    print(f"\nProcessing batch {batch_num} with {len(queries)} queries...")
    start_time = time.time()

    print("收集并发推理任务...")
    for query_info in queries:
        product_id = query_info[0]
        query = query_info[1]
        if pd.isna(product_id) or is_empty(product_id) or is_empty(query):
            print(f"跳过非法测试用例: item={product_id} query={query_info}")
            continue

        task = asyncio.create_task(get_response(session, query, product_id, env, timeout=timeout, version=version))
        tasks.append(task)
    print("开始并发推理...")
    all_responses = await asyncio.gather(*tasks, return_exceptions=True)

    # Process responses and retry failed ones
    retry_queries = []
    retry_indices = []

    for i, response in enumerate(all_responses):
        # Check if the response is an exception
        if isinstance(response, Exception):
            print(f"请求 {i + 1} 失败: {str(response)}")
            retry_queries.append(queries[i])
            retry_indices.append(i)
        # Check if the response indicates a connection error or client disconnection
        elif isinstance(response, dict) and "build_prompt" in response:
            error_msg = response.get("build_prompt", "")
            if "连接错误" in error_msg or "客户端断开连接" in error_msg or "请求超时" in error_msg:
                print(f"请求 {i + 1} 需要重试: {error_msg}")
                retry_queries.append(queries[i])
                retry_indices.append(i)

    # Retry failed requests if any
    retry_count = 0
    while retry_queries and retry_count < max_retries:
        retry_count += 1
        print(f"\n第 {retry_count} 次重试 {len(retry_queries)} 个失败的请求...")
        retry_tasks = []

        for query_info in retry_queries:
            product_id = query_info[0]
            query = query_info[1]
            # Increase timeout for retries
            retry_task = asyncio.create_task(
                get_response(session, query, product_id, env, timeout=timeout * 1.5, version=version))
            retry_tasks.append(retry_task)

        # Wait between retries to avoid overwhelming the server
        await asyncio.sleep(1)

        retry_responses = await asyncio.gather(*retry_tasks, return_exceptions=True)

        # Update the original responses with successful retries
        new_retry_queries = []
        new_retry_indices = []

        for i, (retry_idx, retry_response) in enumerate(zip(retry_indices, retry_responses)):
            if not isinstance(retry_response, Exception) and isinstance(retry_response, dict):
                error_msg = retry_response.get("build_prompt", "")
                if not ("连接错误" in error_msg or "客户端断开连接" in error_msg or "请求超时" in error_msg):
                    # Successful retry
                    all_responses[retry_idx] = retry_response
                    print(f"重试成功: 请求 {retry_idx + 1}")
                else:
                    # Still failed, add to next retry batch if retries remain
                    if retry_count < max_retries:
                        new_retry_queries.append(retry_queries[i])
                        new_retry_indices.append(retry_idx)
                    print(f"重试失败: 请求 {retry_idx + 1} - {error_msg}")
            else:
                # Exception occurred, add to next retry batch if retries remain
                if retry_count < max_retries:
                    new_retry_queries.append(retry_queries[i])
                    new_retry_indices.append(retry_idx)
                print(f"重试异常: 请求 {retry_idx + 1} - {str(retry_response)}")

        # Update retry lists for next iteration
        retry_queries = new_retry_queries
        retry_indices = new_retry_indices

    end_time = time.time()
    duration = end_time - start_time
    print("并发推理完成, 耗时: {:.2f}秒".format(duration))

    # 翻译任务
    print("收集并发翻译任务...")
    translate_tasks = []
    start_time_trans = time.time()
    for i, all_response in enumerate(all_responses):
        try:
            if all_response.get('data', {}).get('text'):
                translate_tasks.append(
                    asyncio.create_task(
                        translate_async(session, all_response['data']['text'], from_lang="印尼语", to_lang="中文"))
                )
            else:
                translate_tasks.append(asyncio.create_task(asyncio.sleep(0)))  # 占位任务
        except:
            translate_tasks.append(asyncio.create_task(asyncio.sleep(0)))  # 占位任务

    print("开始并发翻译...")
    translated_texts = await asyncio.gather(*translate_tasks, return_exceptions=True)
    duration_translate = time.time() - start_time_trans
    print("并发翻译完成, 耗时: {:.2f}秒".format(duration_translate))

    # 打分任务
    if val_score_flag and len(queries[0]) >= 3:
        print("收集并发打分任务...")
        score_tasks = []
        start_time_score = time.time()
        for query_info, all_response in zip(queries, all_responses):
            try:
                if all_response.get('data', {}).get('text'):
                    score_tasks.append(
                        asyncio.create_task(score_ai_ans_parse_async(
                            session,
                            SPU=query_info[0],
                            Question=query_info[1],
                            Answer=query_info[2],
                            Answer_AI=all_response['data']['text'],
                            Tips_info=all_response['build_prompt'],
                            env=env,
                        ))
                    )
                else:
                    score_tasks.append(asyncio.create_task(asyncio.sleep(0)))  # 占位任务
            except:
                score_tasks.append(asyncio.create_task(asyncio.sleep(0)))  # 占位任务

        print("开始并发打分...")
        score_results = await asyncio.gather(*score_tasks, return_exceptions=True)
        duration_score = time.time() - start_time_score
        print("并发打分完成, 耗时: {:.2f}秒".format(duration_score))
    else:
        score_results = [0] * len(queries)

    for i, (query_info, all_response, translated_text, score_result) in enumerate(
            zip(queries, all_responses, translated_texts, score_results)):
        try:
            if all_response.get('data', {}).get('text'):
                response = all_response['data']['text']
                try:
                    response_zn = translated_text if isinstance(translated_text, str) else ""
                except:
                    response_zn = ""
                try:
                    if val_score_flag:
                        response_score = score_result['score']
                        response_reason = score_result['reason']
                        response_score_tag = score_result['tag']
                        response_score_tag_reason = score_result['tag_reason']
                    else:
                        response_score = 0
                        response_reason = ""
                        response_score_tag = ""
                        response_score_tag_reason = ""
                except:
                    response_score = 0
                    response_reason = ""
                    response_score_tag = ""
                    response_score_tag_reason = ""
            else:
                response = ""
                response_zn = ""
                response_score = 0
                response_reason = ""
                response_score_tag = ""
                response_score_tag_reason = ""

            request_id = all_response['request_id']
            request_build_prompt = all_response['build_prompt']
            request_answer_type = all_response['answer_type']
            request_build_model_version = all_response["data"]['model_version']
            request_id_time = all_response["time_cost"] if all_response["time_cost"] != "" else duration
            request_first_token_time = all_response["first_token_time"] if all_response[
                                                                               "first_token_time"] != "" else ""
            request_infer_time = all_response["infer_time"] if all_response["infer_time"] != "" else ""
            request_total_tokens = all_response["total_tokens"] if all_response["total_tokens"] != "" else ""
        except Exception as e:
            response = ""
            response_zn = ""
            request_id = ""
            request_build_prompt = str(e)
            request_answer_type = ""
            request_build_model_version = ""
            response_score = 0
            response_reason = ""
            response_score_tag = ""
            response_score_tag_reason = ""
            request_id_time = duration
            request_first_token_time = ""
            request_infer_time = ""
            request_total_tokens = ""

        # 检测疑似 Markdown 语法
        markdown_detected = 1 if may_have_bad_markdown(response) else 0

        # 构建基础结果字典
        result_dict = {
            "request_id": request_id,
            "机型": query_info[columns_info['product_idx']] if columns_info else query_info[0],
            "问题": query_info[columns_info['query_idx']] if columns_info else query_info[1],
            "推理模型答案-印尼": response,
            "推理模型答案-中文": response_zn,
            "score": response_score,
            "reason": response_reason,
            "score_tag": response_score_tag,
            "score_tag_reason": response_score_tag_reason,
            "推理时间": round(request_id_time, 3),
            "first_token_time": request_first_token_time,
            "infer_time_inner": request_infer_time,
            "model_version": request_build_model_version,
            "prompt": request_build_prompt,
            "answer_type": request_answer_type,
            "total_tokens": request_total_tokens,
            "疑似 markdown": markdown_detected,
        }

        # 添加参考答案（如果存在）
        if columns_info and columns_info['ref_ans_idx'] is not None:
            result_dict["参考答案-印尼"] = query_info[columns_info['ref_ans_idx']]
        else:
            result_dict["参考答案-印尼"] = ""

        # 添加额外的列数据（使用正确的索引）
        if columns_info and columns_info['extra_cols_mapping']:
            for col_name, col_idx in columns_info['extra_cols_mapping'].items():
                if col_idx < len(query_info):
                    result_dict[col_name] = query_info[col_idx]

        results.append(result_dict)
    print(f"Batch {batch_num} completed in {duration:.2f} seconds")
    return results


def save_dict_to_excel(data, output_file, ahead_cols=None):
    # 将字典列表转换为 DataFrame
    df = pd.DataFrame(data)

    # 移除所有值都为空的列
    df = remove_empty_columns(df)

    if not_empty(ahead_cols):
        df = put_cols_ahead(df, ahead_cols)
    # 保存到 Excel 文件
    df.to_excel(output_file, index=False)
    print(f"问答对已保存到 {output_file}")


def load_json_info_for_process(input_file, product_str, query_str, ref_ans, extra_columns=""):
    print("加载数据...", input_file)
    read_info = pd.read_excel(input_file)

    # 构建需要读取的列列表
    columns_to_read = [product_str, query_str]

    # 记录列的位置信息
    columns_info = {
        'product_idx': 0,
        'query_idx': 1,
        'ref_ans_idx': None,
        'extra_cols_mapping': {}  # 列名 -> 索引
    }

    # 添加参考答案列（如果存在）
    has_ref_ans = ref_ans and ref_ans in read_info.columns
    if has_ref_ans:
        columns_to_read.append(ref_ans)
        columns_info['ref_ans_idx'] = len(columns_to_read) - 1

    # 添加额外的列（如果指定）
    extra_col_names = []
    if extra_columns and extra_columns.strip():
        extra_col_list = [col.strip() for col in extra_columns.split(',') if col.strip()]
        for col in extra_col_list:
            if col in read_info.columns and col not in columns_to_read:
                columns_to_read.append(col)
                extra_col_names.append(col)
                columns_info['extra_cols_mapping'][col] = len(columns_to_read) - 1
            elif col not in read_info.columns:
                print(f"警告：列 '{col}' 在文件中不存在，将被忽略")

    qa_list = pd.read_excel(input_file)[columns_to_read].values
    print(f"加载了 {len(columns_to_read)} 列数据：{columns_to_read}")
    print(f"列索引信息：{columns_info}")

    # 返回数据和列名信息
    return qa_list, columns_to_read, extra_col_names, columns_info


async def main():
    # Configuration
    # 读取配置文件
    with open("./val_batch_run.yaml", "r", encoding="utf-8") as file:
        config = yaml.safe_load(file)

    # 从配置文件中获取参数
    BATCH_SIZE = config["batch_size"]
    input_file = config["input_file"]
    output_file = config["output_file"]
    val_res_file = config["val_res_file"]
    print('保存目标路径', output_file)
    val_score_flag = config["val_score_flag"]
    product_str = config["product_str"]
    query_str = config["query_str"]
    ref_ans = config["ref_ans"]

    # BATCH_SIZE = 10 # 每次并发请求的数量
    # input_file = '/Users/<USER>/workspace/sale_copilot_info/mark_2kdata/4.18/评测集v0.1(580)-25.4.18更新.xlsx'
    # output_file = input_file.replace('.xlsx', '-infer-test.xlsx')
    # val_score_flag = False
    # product_str = ''
    # query_str = ''
    # ref_ans = ''

    # qa_list is numpy array, shape [n, 5]
    qa_list, columns_to_read, extra_col_names, columns_info = load_json_info_for_process(input_file, product_str,
                                                                                         query_str, ref_ans)
    # qa_list = qa_list[:20]
    env = 'preview'
    print(f"Loaded {len(qa_list)} queries")

    # Process queries in batches
    all_results = []
    total_batches = (len(qa_list) + BATCH_SIZE - 1) // BATCH_SIZE

    async with aiohttp.ClientSession() as session:
        for batch_num in range(total_batches):
            start_idx = batch_num * BATCH_SIZE
            end_idx = min((batch_num + 1) * BATCH_SIZE, len(qa_list))
            batch_qa_list = qa_list[start_idx:end_idx]

            batch_results = await process_batch(session, batch_qa_list, env, val_score_flag, batch_num + 1,
                                                max_retries=2, timeout=60, extra_col_names=extra_col_names,
                                                columns_info=columns_info)
            all_results.extend(batch_results)
            save_dict_to_excel(all_results, output_file)

            # # Add a small delay between batches to prevent overwhelming the server
            # if batch_num < total_batches - 1:
            #     await asyncio.sleep(0.5)

    count_first_token_time_count = sum(1 for result in all_results if result.get("first_token_time") != "")
    count_first_token_time = sum(
        result.get("first_token_time", 0) for result in all_results if result.get("first_token_time") != "")
    count_first_token_time_average = count_first_token_time / count_first_token_time_count if count_first_token_time_count > 0 else 0

    count_infer_inner_time_count = sum(1 for result in all_results if
                                       result.get("first_token_time") != "" and result.get(
                                           "infer_time_inner") != "")
    count_infer_inner_time = sum(result.get("infer_time_inner", 0) for result in all_results if
                                 result.get("first_token_time") != "" and result.get("infer_time_inner") != "")
    count_infer_inner_time_average = count_infer_inner_time / count_infer_inner_time_count if count_first_token_time_count > 0 else 0

    count_infer_feel_time_count = sum(
        1 for result in all_results if result.get("first_token_time") != "" and result.get("推理时间") != "")
    count_infer_feel_time = sum(result.get("推理时间", 0) for result in all_results if
                                result.get("first_token_time") != "" and result.get("推理时间") != "")
    count_infer_feel_time_average = count_infer_feel_time / count_infer_feel_time_count if count_first_token_time_count > 0 else 0

    # 统计 score 等于 2 的个数
    count_score_2 = sum(1 for result in all_results if result.get("score") == 2)
    # 计算准确率
    accuracy = count_score_2 / len(all_results) if all_results else 0
    infer_success_rate = count_first_token_time_count / len(qa_list) if len(qa_list) > 0 else 0

    val_res = []
    val_res.append({
        "AI打分准确率": f"{accuracy:.2%}",
        "平均首Token时间": count_first_token_time_average,
        "平均infer时间": count_infer_inner_time_average,
        "体感平均infer时间": count_infer_feel_time_average,
        "infer_success_rate": f"{infer_success_rate:.2%}",
        "infer_success": count_first_token_time_count,
        "infer_counts": len(qa_list),
        "infer_fail": len(qa_list) - count_first_token_time_count,
    })
    save_dict_to_excel(val_res, val_res_file)
    print(f"Score 等于 2 的个数: {count_score_2}")
    print(f"准确率: {accuracy:.2%}")
    print(f"推理成功率: {infer_success_rate:.2%}")
    print(f"\nAll batches completed. Total queries processed: {len(all_results)}")
    print(f"Results saved to {output_file}")


if __name__ == "__main__":
    asyncio.run(main())
