from fastapi import HTTPException

from core.schema.chat_base import MessageType
from core.schema.chat_request import Chat<PERSON><PERSON>quest, Role
from core.schema.translation_base import TranslationRequest
from util.common_util import is_empty, not_empty


def validate_chat_request(chat_request: ChatRequest, logger):
    # ToDo(hm): 传入的 item_name 要校验吗：比如传入的 item 不在我们的数据集中
    if chat_request.version == 0:
        validate_chat_request_430(chat_request, logger)
        return

    if chat_request.version in (1, 2):
        validate_chat_request_530(chat_request, logger)
        return


def validate_chat_request_430(chat_request: ChatRequest, logger):
    common_validate(chat_request, logger)

    if is_empty(chat_request.item_name):
        # 当前 item_name 必传且非空串
        logger.error(f"收到非法的输入，item_name 不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="收到非法的输入，item_name 不能为空")

    if is_empty(chat_request.item_id):
        # 当前 item_id 必传且非空串
        logger.error(f"收到非法的输入，item_id 不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="收到非法的输入，item_id 不能为空")

    for chat_round in chat_request.chat_history:
        if is_empty(chat_round.messages):
            logger.error(f"收到非法的输入，单轮对话（messages）不能为空：{chat_request}")
            raise HTTPException(status_code=400, detail="单轮对话（messages）不能为空")

        for message in chat_round.messages:
            if message.type == MessageType.TEXT:
                if is_empty(message.content):
                    logger.error(f"收到非法的输入，消息内容（content）不能为空：{chat_request}")
                    raise HTTPException(status_code=400, detail="消息内容（content）不能为空")

                if len(message.content) >= 2048:
                    logger.error(f"收到非法的输入，消息内容（content）太长：{message.content[:20]}...")
                    raise HTTPException(
                        status_code=400, detail=f"消息内容（content）太长：{message.content[:20]}..."
                    )

    # validate whether the last round is from user
    ending_chat_round = chat_request.chat_history[-1]
    if ending_chat_round.role != Role.USER:
        raise HTTPException(
            status_code=400, detail=f"最后一条消息不是来自用户，而是 {ending_chat_round.role.name}"
        )


def common_validate(chat_request: ChatRequest, logger):
    if chat_request.version not in (0, 1, 2):
        logger.error(f"收到非法的输入 (version={chat_request.version}")
        raise HTTPException(status_code=400, detail=f"收到非法的输入 version={chat_request.version}")

    if chat_request.area is None:
        logger.error(f"收到非法的输入，area 不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="收到非法输入 area 不能为空")

    if chat_request.language is None:
        logger.error(f"收到非法的输入，language 不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="收到非法输入 language 不能为空")

    if chat_request.conversation_id is None:
        logger.error(f"收到非法的输入，conversation_id 不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="收到非法输入 conversation_id 不能为空")

    if chat_request.request_id is None:
        logger.error(f"收到非法的输入，request_id 不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="收到非法输入 request_id 不能为空")

    if is_empty(chat_request.chat_history):
        logger.error(f"收到非法的输入，会话历史（chat_history）不能为空：{chat_request}")
        raise HTTPException(status_code=400, detail="会话历史（chat_history）不能为空")


# 530 版本：如果用户确认了机型，后端会把问题拿到最后一条消息中
def validate_chat_request_530(chat_request: ChatRequest, logger):
    common_validate(chat_request, logger)

    # 校验最后两条消息的角色是否相同，防止用户选择两次机型
    # ToDo(hm): 这里重新写
    # if len(chat_request.chat_history) >= 2 and (
    #         chat_request.chat_history[-1].role == chat_request.chat_history[-2].role) and chat_request.chat_history[
    #     -1].type == MessageType.ITEM_CONFIRM and chat_request.chat_history[-2].type == MessageType.ITEM_CONFIRM:
    #     logger.error(f"收到非法的输入，最后两条消息角色相同且均为确认机型类型：{chat_request}")
    #     raise HTTPException(status_code=400, detail="最后两条消息角色相同且均为确认机型类型")

    # 最后一条消息为用户消息
    if chat_request.chat_history[-1].role == Role.USER:
        # 如果最后一条消息是用户消息且类型为确认机型
        if chat_request.chat_history[-1].messages[0].type == MessageType.ITEM_CONFIRM:
            # 如果用户确认机型且没有选中机型，则报错
            if chat_request.chat_history[-1].messages[0].selected_item is None:
                if chat_request.chat_history[-1].messages[0].item_list is None:
                    logger.error(f"收到非法的输入，用户确认机型且没有选中机型：{chat_request}")
                    raise HTTPException(status_code=400, detail="用户确认机型且没有选中机型")
                logger.error(f"收到非法的输入，用户确认机型且没有选中机型：{chat_request}")
                raise HTTPException(status_code=400, detail="用户确认机型且没有选中机型")
            # 如果用户确认机型且选中机型，则校验问答内容
            if len(chat_request.chat_history) == 1:
                if is_empty(chat_request.chat_history[-1].messages[0].content):
                    logger.error(f"收到非法的输入，用户确认机型且聊天历史长度为1，content内容不能为空：{chat_request}")
                    raise HTTPException(status_code=400, detail="用户确认机型且聊天历史长度为1，content内容不能为空")
            if len(chat_request.chat_history) >= 3:
                if chat_request.chat_history[-3].role != Role.USER:
                    logger.error(f"收到非法的输入，传递历史消息非法，聊天历史奇数索引非用户：{chat_request}")
                    raise HTTPException(status_code=400, detail="传递历史消息非法，聊天历史奇数索引非用户")
                if chat_request.chat_history[-3].messages[0].content is None:
                    logger.error(f"收到非法的输入，传递历史消息非法，历史content不能为空：{chat_request}")
                    raise HTTPException(status_code=400, detail="传递历史消息非法，历史content不能为空")

    for chat_round in chat_request.chat_history:
        if is_empty(chat_round.messages):
            logger.error(f"收到非法的输入，单轮对话（messages）不能为空：{chat_request}")
            raise HTTPException(status_code=400, detail="单轮对话（messages）不能为空")

        for message in chat_round.messages:
            if message.type == MessageType.TEXT:
                if is_empty(message.content):
                    logger.error(f"收到非法的输入，消息内容（content）不能为空：{chat_request}")
                    raise HTTPException(status_code=400, detail="消息内容（content）不能为空")

                if len(message.content) >= 2048:
                    logger.error(f"收到非法的输入，消息内容（content）太长：{message.content[:20]}...")
                    raise HTTPException(
                        status_code=400, detail=f"消息内容（content）太长：{message.content[:20]}..."
                    )

            if not_empty(message.item_list):
                for item in message.item_list:
                    if is_empty(item.item_id):
                        logger.error(f"收到非法的输入，用户选择机型（item_id）不能为空：{chat_request}")
                        raise HTTPException(status_code=400, detail="用户选择机型（item_id）不能为空")

                    if is_empty(item.item_name):
                        logger.error(f"收到非法的输入，用户选择机型（item_name）不能为空：{chat_request}")
                        raise HTTPException(status_code=400, detail="用户选择机型（item_name）不能为空")

            if message.selected_item is not None:
                if is_empty(message.selected_item.item_id):
                    logger.error(f"收到非法的输入，吸顶机型（selected_item_id）不能为空：{chat_request}")
                    raise HTTPException(status_code=400, detail="吸顶机型（selected_item_id）不能为空")

                if is_empty(message.selected_item.item_name):
                    logger.error(f"收到非法的输入，吸顶机型（selected_item_name）不能为空：{chat_request}")
                    raise HTTPException(status_code=400, detail="吸顶机型（selected_item_name）不能为空")

    # validate whether the last round is from user
    ending_chat_round = chat_request.chat_history[-1]
    if ending_chat_round.role != Role.USER:
        raise HTTPException(
            status_code=400, detail=f"最后一条消息不是来自用户，而是 {ending_chat_round.role.name}"
        )


def validate_translation_request(translation_request: TranslationRequest, logger):
    if is_empty(translation_request.content):
        logger.error(f"收到非法的输入，待翻译文本不能为空：{translation_request}")
        raise HTTPException(status_code=400, detail="待翻译文本不能为空")

    if is_empty(translation_request.request_id):
        logger.error(f"收到非法的输入，请求id不能为空：{translation_request}")
        raise HTTPException(status_code=400, detail="请求id不能为空")
